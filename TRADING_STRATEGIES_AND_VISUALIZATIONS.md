# Trading Strategies and Backtest Visualizations Documentation

## Overview
This document provides a comprehensive overview of all trading strategies implemented in this project and details the backtest visualization system. This project is a sophisticated trading rule analysis system that tests multiple buy/sell rules across different market conditions with advanced filtering and position management.

## Trading Strategies

### 1. **Unified Evaluation Strategy**
- **Description**: All buy rules are evaluated simultaneously on each candle
- **Position Management**: Multiple concurrent positions allowed (configurable limit)
- **Rule Interaction**: Rules can trigger independently, creating multiple simultaneous trades
- **Exit Strategy**: Uses SL/TP for position exits
- **Use Case**: Comprehensive strategy testing with realistic market conditions

### 2. **Independent Evaluation Strategy**
- **Description**: Each buy rule is tested in complete isolation
- **Position Management**: Single position per rule test
- **Rule Interaction**: No interaction between rules - pure individual performance
- **Exit Strategy**: Only SL/TP exits (sell rules disabled for isolation)
- **Use Case**: Measuring true individual rule performance without interference

### 3. **SL/TP Only Strategy**
- **Description**: Pure stop-loss and take-profit based trading
- **Position Management**: Configurable (single or multiple positions)
- **Rule Interaction**: Buy rules trigger entries, but no sell rules
- **Exit Strategy**: Only stop-loss and take-profit exits
- **Use Case**: Testing raw buy signal quality without sell rule complexity

### 4. **Isolated Buy Rules Strategy**
- **Description**: Buy rules tested with sell rule blocking
- **Position Management**: Single or multiple positions
- **Rule Interaction**: Buy signals blocked when any sell rule is active
- **Exit Strategy**: Combined sell rules and SL/TP
- **Use Case**: Testing buy rules in isolation from conflicting sell signals

### 5. **Grid Search Strategy**
- **Description**: Systematic testing of multiple SL/TP combinations
- **Position Management**: Configurable
- **Rule Interaction**: Standard rule interactions
- **Exit Strategy**: Variable SL/TP parameters tested systematically
- **Use Case**: Optimizing stop-loss and take-profit parameters

### 6. **Short-Only Futures Strategy**
- **Description**: Futures trading with only short positions
- **Position Management**: Independent sell rule evaluation with 10x leverage
- **Rule Interaction**: Sell rules trigger short entries independently
- **Exit Strategy**: Inverted SL/TP (SL at +1.25%, TP at -0.75% for shorts)
- **Use Case**: Bear market trading and hedging strategies

### 7. **Filter-Enhanced Strategy**
- **Description**: Trading with advanced global market filters
- **Position Management**: Configurable with filter-based trade limiting
- **Rule Interaction**: All trades subject to global filter conditions
- **Exit Strategy**: Standard SL/TP with filter-based entry restrictions
- **Use Case**: Risk management in different market conditions

## Global Market Filters

### 1. **EMA200 Filter**
- **Implementation**: Uses daily EMA200 calculated from proper daily candles (not minute-based)
- **Logic**: Blocks or limits trades when price is below daily EMA200
- **Configuration**: 
  - `GLOBAL_EMA200_BELOW_MAX_TRADES = 0`: Block ALL trades below EMA200
  - `GLOBAL_EMA200_BELOW_MAX_TRADES = 1`: Allow max 1 concurrent trade below EMA200
- **Data Requirements**: Minimum 288,000 candles (200 days) for reliable calculation

### 2. **RSI Overbought Filter**
- **Implementation**: Uses daily RSI calculated from daily candles
- **Logic**: Blocks trades when daily RSI > 75 (overbought conditions)
- **Configuration**: Configurable threshold (default 75)

### 3. **MA Waterfall Filter**
- **Implementation**: Uses daily MA7, MA25, MA50
- **Logic**: Blocks or limits trades when MA50 > MA25 (bearish structure)
- **Configuration**: Configurable trade limits when filter is active

### 4. **Loss Protection Filters**
- **Daily Loss Filter**: Stops trading for 24 hours after 3% daily loss
- **Consecutive Loss Filter**: Stops trading after configurable consecutive losses (default 5)

## Position Management Systems

### 1. **Single Position Mode**
- **Description**: Traditional one-position-at-a-time trading
- **Implementation**: `MAX_CONCURRENT_TRADES = 1`
- **Use Case**: Conservative trading, rule isolation testing

### 2. **Multiple Position Mode**
- **Description**: Allows multiple simultaneous positions
- **Implementation**: `MAX_CONCURRENT_TRADES > 1` (e.g., 2, 5)
- **Risk Management**: Capital divided by max positions for position sizing
- **Use Case**: Diversified signal capture, realistic trading simulation

### 3. **Rule-Specific Position Tracking**
- **Description**: Prevents overlapping trades from the same rule
- **Implementation**: Each rule can only have one active position at a time
- **Use Case**: Preventing signal spam from individual rules

## Configuration Classes

### Base Configurations
1. **Config**: Base configuration with default parameters
2. **QuickTestConfig**: 10k candles for rapid development testing
3. **FullAnalysisConfig**: Full dataset analysis with optimized settings

### Specialized Configurations
1. **ResearchConfig**: Detailed tracking with comprehensive logging
2. **GridSearchConfig**: SL/TP parameter optimization
3. **IndependentEvaluationConfig**: Isolated rule testing
4. **OptimizedRulesConfig**: Testing only pre-selected high-performance rules

### Futures Configurations
1. **ShortOnlyFuturesConfig**: Short-only futures trading
2. **FullShortOnlyFuturesConfig**: Full dataset short futures analysis

## Backtest Visualization System

### 1. **HTML Dashboard Generation**

#### **Core Visualization Engine**
- **File**: `html_visualizer.py`
- **Technology**: Pure HTML/CSS/JavaScript with embedded charts
- **Libraries**: Chart.js for interactive charts, custom CSS for styling
- **Output**: Self-contained HTML files with all data embedded

#### **Dashboard Components**

**Performance Summary Section**
- **Metrics Displayed**: Total return, win rate, profit factor, Sharpe ratio, max drawdown
- **Implementation**: HTML tables with color-coded performance indicators
- **Styling**: Green/red color coding based on performance thresholds

**Equity Curve Visualization**
- **Chart Type**: Line chart showing capital growth over time
- **Implementation**: Chart.js line chart with time-series data
- **Features**: Interactive tooltips, zoom functionality, drawdown highlighting
- **Data Format**: Time-indexed capital values with trade markers

**Rule Performance Tables**
- **Dual Ranking System**: 
  - Primary table sorted by Total Return
  - Secondary table sorted by Win Rate
- **Columns**: Rule name, trades, win rate, total return, profit factor, avg gain/loss, max drawdown
- **Filtering**: Color-coded rows based on performance criteria
- **Interactivity**: Sortable columns, hover effects

**Trade Distribution Charts**
- **Chart Types**: Bar charts for win/loss distribution, pie charts for outcome ratios
- **Implementation**: Chart.js bar and doughnut charts
- **Data**: Trade outcomes, duration analysis, PnL distribution

#### **Specialized Dashboards**

**Futures Trading Dashboard**
- **File**: Methods in `html_visualizer.py` for futures-specific metrics
- **Features**: Leverage-adjusted returns, short position analysis
- **Metrics**: Futures-specific risk metrics, margin requirements

**Correlation Analysis Dashboard**
- **Implementation**: Heatmap visualization of rule correlations
- **Technology**: Custom JavaScript for correlation matrix rendering
- **Use Case**: Portfolio optimization and rule diversification

### 2. **Performance Tracking System**

#### **Core Tracking Engine**
- **File**: `performance_tracker.py`
- **Functionality**: Real-time performance calculation and aggregation
- **Data Storage**: In-memory dictionaries with periodic serialization

#### **Metrics Calculation**

**Basic Metrics**
- **Win Rate**: Percentage of profitable trades
- **Total Return**: Cumulative percentage return
- **Profit Factor**: Gross profit / Gross loss
- **Average Gain/Loss**: Mean profit and loss per trade

**Advanced Metrics**
- **Sharpe Ratio**: Risk-adjusted return calculation
- **Maximum Drawdown**: Peak-to-trough decline measurement
- **Trade Duration**: Average, minimum, maximum holding periods
- **Consecutive Wins/Losses**: Streak analysis

**Custom Scoring System**
- **Formula**: `0.25*return + 0.2*win_rate + 0.2*log(profit_factor) - 0.15*max_drawdown + 0.1*log(trades) + 0.1*avg_gain_loss/0.5`
- **Purpose**: Weighted performance ranking considering multiple factors
- **Implementation**: Logarithmic scaling for profit factor and trade count

#### **Rule-by-Rule Analysis**
- **Individual Tracking**: Each rule's performance tracked separately
- **Aggregation**: Portfolio-level metrics calculated from individual rules
- **Filtering**: Performance-based rule selection using configurable thresholds

### 3. **Data Export and Persistence**

#### **Results Storage**
- **Directory**: `results/` folder with timestamped files
- **Formats**: 
  - JSON for structured data
  - CSV for tabular analysis
  - HTML for interactive dashboards

#### **File Naming Convention**
- **Pattern**: `{strategy_type}_{timestamp}.{extension}`
- **Examples**: 
  - `full_analysis_20241202_143022.html`
  - `independent_evaluation_20241202_143022.json`
  - `short_futures_dashboard_full_20241202_143022.html`

#### **Data Structure**
```json
{
  "strategy_config": {...},
  "rule_performance": {
    "rule_name": {
      "trades": 150,
      "win_rate": 65.5,
      "total_return": 23.4,
      "profit_factor": 1.8,
      "max_drawdown": -8.2,
      "trades_data": [...]
    }
  },
  "portfolio_metrics": {...},
  "execution_time": "2024-12-02T14:30:22"
}
```

### 4. **Interactive Features**

#### **Chart Interactivity**
- **Zoom and Pan**: Time-series charts support zooming and panning
- **Tooltips**: Hover tooltips showing detailed trade information
- **Legend Toggle**: Click legend items to show/hide data series

#### **Table Functionality**
- **Sorting**: Click column headers to sort by different metrics
- **Filtering**: Color-coded rows for quick performance assessment
- **Responsive Design**: Tables adapt to different screen sizes

#### **Real-time Updates**
- **Progress Tracking**: Live progress bars during backtesting
- **Status Updates**: Real-time console output with progress indicators
- **Memory Monitoring**: System resource usage tracking

### 5. **Visualization Best Practices**

#### **Performance Optimization**
- **Data Sampling**: Large datasets sampled for chart performance
- **Lazy Loading**: Charts rendered on-demand to reduce initial load time
- **Efficient Rendering**: Optimized JavaScript for smooth interactions

#### **User Experience**
- **Consistent Styling**: Unified color scheme and typography
- **Clear Labeling**: Descriptive axis labels and chart titles
- **Accessibility**: High contrast colors and readable fonts

#### **Data Integrity**
- **Validation**: Input data validated before visualization
- **Error Handling**: Graceful handling of missing or invalid data
- **Backup Storage**: Multiple export formats for data redundancy

This visualization system provides comprehensive analysis capabilities for trading strategy evaluation, enabling detailed performance assessment and optimization through interactive, web-based dashboards.

## Technical Implementation Details

### 1. **Data Flow Architecture**

#### **Input Data Processing**
- **Source**: CSV files with OHLCV minute-level data
- **Size**: Typically 800k+ candles for reliable EMA200 calculation
- **Processing**: Real-time indicator calculation using TA-Lib library
- **Memory Management**: Efficient pandas operations with chunked processing for large datasets

#### **Indicator Calculation Pipeline**
1. **Raw Data Loading**: CSV parsing with datetime indexing
2. **Daily Resampling**: 1-minute data converted to daily candles for daily indicators
3. **Technical Indicators**: 100+ indicators calculated using TA-Lib
4. **Data Validation**: NaN handling and data quality checks
5. **Memory Optimization**: Selective column retention for worker processes

#### **Backtesting Execution Flow**
1. **Configuration Loading**: Strategy parameters loaded from config classes
2. **Rule Initialization**: Buy/sell rules loaded from modular rule files
3. **Multiprocessing Setup**: Worker processes spawned for parallel rule testing
4. **Trade Execution**: Simulated trading with realistic slippage and timing
5. **Results Aggregation**: Performance metrics calculated and consolidated

### 2. **Multiprocessing Architecture**

#### **Worker Process Design**
- **Isolation**: Each rule tested in separate process for true isolation
- **Data Sharing**: Essential data columns shared via multiprocessing.Manager
- **Progress Tracking**: Real-time progress updates using shared dictionaries
- **Error Handling**: Graceful failure handling with process restart capability

#### **Memory Optimization**
- **Selective Data**: Only essential columns passed to workers
- **Batch Processing**: Large datasets processed in configurable batches
- **Garbage Collection**: Explicit memory cleanup between rule tests
- **Resource Monitoring**: CPU and memory usage tracking

### 3. **Rule System Architecture**

#### **Modular Rule Design**
- **File Structure**: Separate files for buy rules, sell rules, and filter rules
- **Function Signature**: Standardized function interface for all rules
- **Parameter Access**: Rules access indicators via optimized caching system
- **Performance Optimization**: Vectorized operations where possible

#### **Rule Categories**
1. **Buy Rules**: Entry signal generation (29 optimized rules)
2. **Sell Rules**: Exit signal generation (configurable)
3. **Filter Rules**: Trade blocking conditions (19 filter rules)
4. **Optimized Rules**: Pre-selected high-performance rule subset

### 4. **Performance Analysis Engine**

#### **Real-time Metrics Calculation**
- **Trade Tracking**: Individual trade records with entry/exit details
- **Portfolio Metrics**: Aggregated performance across all rules
- **Risk Metrics**: Drawdown, volatility, and risk-adjusted returns
- **Correlation Analysis**: Inter-rule correlation for diversification

#### **Statistical Analysis**
- **Distribution Analysis**: Trade outcome distributions and statistics
- **Trend Analysis**: Performance trends over time periods
- **Seasonality**: Time-based performance patterns
- **Outlier Detection**: Identification of exceptional trades and periods

### 5. **Visualization Data Pipeline**

#### **Data Transformation**
1. **Raw Results**: Trade-level data from backtesting engine
2. **Aggregation**: Rule-level and portfolio-level metric calculation
3. **Time Series**: Equity curve and drawdown series generation
4. **Formatting**: Data formatted for JavaScript chart libraries
5. **Embedding**: All data embedded in self-contained HTML files

#### **Chart Data Structures**
```javascript
// Equity Curve Data
{
  labels: ["2024-01-01", "2024-01-02", ...],
  datasets: [{
    label: "Portfolio Value",
    data: [100000, 100150, 99980, ...],
    borderColor: "rgb(75, 192, 192)"
  }]
}

// Performance Table Data
{
  rule_name: "Rule 1: RSI Oversold",
  trades: 45,
  win_rate: 67.5,
  total_return: 12.3,
  profit_factor: 1.8,
  max_drawdown: -5.2
}
```

### 6. **Configuration Management**

#### **Hierarchical Configuration**
- **Base Config**: Default parameters for all strategies
- **Strategy Configs**: Specialized configurations inheriting from base
- **Runtime Overrides**: Command-line parameter overrides
- **Validation**: Configuration validation with error reporting

#### **Parameter Categories**
1. **Data Parameters**: Dataset size, file paths, date ranges
2. **Trading Parameters**: SL/TP, position sizing, concurrent trades
3. **Filter Parameters**: Global filter settings and thresholds
4. **Performance Parameters**: Optimization settings and thresholds
5. **Output Parameters**: Visualization and export settings

### 7. **Error Handling and Logging**

#### **Comprehensive Error Management**
- **Rule-Level Errors**: Individual rule failures don't stop entire backtest
- **Data Errors**: Missing data handled gracefully with fallbacks
- **Memory Errors**: Automatic fallback to sequential processing
- **Visualization Errors**: Partial rendering with error notifications

#### **Logging System**
- **Progress Logging**: Real-time progress updates with timestamps
- **Performance Logging**: Execution time and resource usage tracking
- **Error Logging**: Detailed error messages with stack traces
- **Debug Logging**: Configurable debug output for development

### 8. **Extensibility and Modularity**

#### **Plugin Architecture**
- **Rule Plugins**: Easy addition of new trading rules
- **Indicator Plugins**: Custom indicator integration
- **Visualization Plugins**: Additional chart types and dashboards
- **Export Plugins**: New output formats and destinations

#### **API Design**
- **Standardized Interfaces**: Consistent function signatures across modules
- **Data Contracts**: Well-defined data structures and formats
- **Version Compatibility**: Backward compatibility for configuration files
- **Documentation**: Comprehensive inline documentation and examples

This technical architecture enables scalable, maintainable, and extensible trading strategy analysis with professional-grade performance tracking and visualization capabilities.
