#!/usr/bin/env python3
"""
Test script to verify that the EMA200 filter fix is working correctly
Tests different GLOBAL_EMA200_BELOW_MAX_TRADES settings to ensure proper trade limiting
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backtesting_engine import BacktestingEngine, check_global_market_filters_worker
from config import Config, FullAnalysisConfig
from indicators import TechnicalIndicators

def test_ema200_filter_logic():
    """Test the EMA200 filter logic with different settings"""
    print("🧪 TESTING EMA200 FILTER FIX")
    print("=" * 60)
    
    # Test configurations
    test_configs = [
        {"name": "Block All Trades (max_trades=0)", "max_trades": 0, "expected_behavior": "No trades when below EMA200"},
        {"name": "Allow 1 Trade (max_trades=1)", "max_trades": 1, "expected_behavior": "Max 1 concurrent trade when below EMA200"},
        {"name": "Allow 2 Trades (max_trades=2)", "max_trades": 2, "expected_behavior": "Max 2 concurrent trades when below EMA200"}
    ]
    
    for test_config in test_configs:
        print(f"\n📊 Testing: {test_config['name']}")
        print(f"   Expected: {test_config['expected_behavior']}")
        print("-" * 50)
        
        # Create test configuration
        config = Config()
        config.CURRENT_DATASET_SIZE = 50000  # Use reasonable sample size
        config.ENABLE_GLOBAL_MARKET_FILTERS = True
        config.GLOBAL_EMA200_FILTER = True
        config.GLOBAL_EMA200_BELOW_MAX_TRADES = test_config['max_trades']
        config.MAX_CONCURRENT_TRADES = 5  # Allow multiple positions for testing
        config.USE_OPTIMIZED_RULES_ONLY = True
        
        print(f"   Config: GLOBAL_EMA200_BELOW_MAX_TRADES = {config.GLOBAL_EMA200_BELOW_MAX_TRADES}")
        print(f"   Config: MAX_CONCURRENT_TRADES = {config.MAX_CONCURRENT_TRADES}")
        
        # Initialize engine
        try:
            engine = BacktestingEngine(config)
            print(f"   ✅ Engine initialized with {len(engine.df):,} candles")
            
            # Check if we have EMA200 data
            if 'DAILY_EMA_200' not in engine.df.columns:
                print(f"   ❌ DAILY_EMA_200 column not found in data")
                continue
                
            # Test the filter logic directly
            test_filter_logic(engine, config, test_config)
            
        except Exception as e:
            print(f"   ❌ Error initializing engine: {e}")
            continue

def test_filter_logic(engine, config, test_config):
    """Test the filter logic with sample data points"""
    
    # Find indices where price is below EMA200
    below_ema_indices = []
    above_ema_indices = []
    
    for idx in range(1000, min(10000, len(engine.df))):  # Sample range
        if idx < len(engine.df) and 'DAILY_EMA_200' in engine.df.columns:
            current_price = engine.df['close'].iloc[idx]
            ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
            
            if not pd.isna(ema200_value):
                if current_price < ema200_value:
                    below_ema_indices.append(idx)
                else:
                    above_ema_indices.append(idx)
    
    print(f"   📈 Found {len(below_ema_indices)} indices below EMA200")
    print(f"   📈 Found {len(above_ema_indices)} indices above EMA200")
    
    if len(below_ema_indices) == 0:
        print(f"   ⚠️  No data points below EMA200 found in sample")
        return
    
    # Test filter behavior when below EMA200
    test_below_ema_behavior(engine, config, test_config, below_ema_indices[:20])  # Test first 20
    
    # Test filter behavior when above EMA200  
    test_above_ema_behavior(engine, config, test_config, above_ema_indices[:10])  # Test first 10

def test_below_ema_behavior(engine, config, test_config, test_indices):
    """Test filter behavior when price is below EMA200"""
    print(f"   🔍 Testing behavior when price BELOW EMA200...")
    
    blocked_count = 0
    allowed_count = 0
    max_trades = test_config['max_trades']
    
    # Simulate different position scenarios
    test_scenarios = [
        {"positions": [], "description": "No existing positions"},
        {"positions": [create_mock_position(test_indices[0], engine, below_ema=True)], "description": "1 position opened below EMA200"},
        {"positions": [create_mock_position(test_indices[0], engine, below_ema=True), 
                      create_mock_position(test_indices[1], engine, below_ema=True)], "description": "2 positions opened below EMA200"},
        {"positions": [create_mock_position(test_indices[0], engine, below_ema=False)], "description": "1 position opened above EMA200"},
    ]
    
    for scenario in test_scenarios:
        print(f"     📋 Scenario: {scenario['description']}")
        
        # Test a few indices for this scenario
        for idx in test_indices[:3]:
            # Test worker filter
            essential_columns = ['open', 'high', 'low', 'close', 'volume', 'DAILY_EMA_200']
            worker_df = engine.df[essential_columns].iloc[0:len(engine.df)]
            
            worker_config = {
                'ENABLE_GLOBAL_MARKET_FILTERS': True,
                'GLOBAL_EMA200_FILTER': True,
                'GLOBAL_EMA200_BELOW_MAX_TRADES': max_trades,
            }
            
            filter_result = check_global_market_filters_worker(worker_df, idx, worker_config, [], scenario['positions'])
            
            current_price = engine.df['close'].iloc[idx]
            ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
            
            if filter_result:
                allowed_count += 1
                status = "✅ ALLOWED"
            else:
                blocked_count += 1
                status = "❌ BLOCKED"
            
            print(f"       Index {idx}: Price ${current_price:.0f} < EMA200 ${ema200_value:.0f} → {status}")
    
    print(f"     📊 Results: {allowed_count} allowed, {blocked_count} blocked")
    
    # Validate expected behavior
    if max_trades == 0:
        if blocked_count > 0:
            print(f"     ✅ CORRECT: Trades blocked when max_trades=0")
        else:
            print(f"     ❌ ERROR: Expected all trades to be blocked when max_trades=0")
    else:
        print(f"     ℹ️  With max_trades={max_trades}, filter should allow trades up to limit")

def test_above_ema_behavior(engine, config, test_config, test_indices):
    """Test filter behavior when price is above EMA200"""
    print(f"   🔍 Testing behavior when price ABOVE EMA200...")
    
    allowed_count = 0
    blocked_count = 0
    
    for idx in test_indices:
        # Test worker filter
        essential_columns = ['open', 'high', 'low', 'close', 'volume', 'DAILY_EMA_200']
        worker_df = engine.df[essential_columns].iloc[0:len(engine.df)]
        
        worker_config = {
            'ENABLE_GLOBAL_MARKET_FILTERS': True,
            'GLOBAL_EMA200_FILTER': True,
            'GLOBAL_EMA200_BELOW_MAX_TRADES': test_config['max_trades'],
        }
        
        filter_result = check_global_market_filters_worker(worker_df, idx, worker_config, [], [])
        
        current_price = engine.df['close'].iloc[idx]
        ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
        
        if filter_result:
            allowed_count += 1
            status = "✅ ALLOWED"
        else:
            blocked_count += 1
            status = "❌ BLOCKED"
        
        print(f"     Index {idx}: Price ${current_price:.0f} > EMA200 ${ema200_value:.0f} → {status}")
    
    print(f"     📊 Results: {allowed_count} allowed, {blocked_count} blocked")
    
    # When above EMA200, trades should always be allowed (EMA200 filter only applies below)
    if blocked_count == 0:
        print(f"     ✅ CORRECT: All trades allowed when above EMA200")
    else:
        print(f"     ❌ ERROR: Trades should not be blocked when above EMA200")

def create_mock_position(idx, engine, below_ema=True):
    """Create a mock position for testing"""
    if below_ema:
        # Create position that was opened when price was below EMA200
        entry_price = engine.df['DAILY_EMA_200'].iloc[idx] * 0.95  # 5% below EMA200
    else:
        # Create position that was opened when price was above EMA200
        entry_price = engine.df['DAILY_EMA_200'].iloc[idx] * 1.05  # 5% above EMA200
    
    return {
        'entry_time': f"Index_{idx}",
        'entry_price': entry_price,
        'quantity': 100,
        'position_value': 1000,
        'entry_idx': idx
    }

def test_filter_with_larger_dataset():
    """Test the filter with a larger dataset that has valid EMA200 data"""
    print(f"\n🚀 TESTING WITH LARGER DATASET FOR VALID EMA200")
    print("=" * 60)

    # Use larger dataset to get valid EMA200 data
    config = Config()
    config.CURRENT_DATASET_SIZE = 300000  # Use 300k candles to ensure EMA200 data
    config.ENABLE_GLOBAL_MARKET_FILTERS = True
    config.GLOBAL_EMA200_FILTER = True
    config.GLOBAL_EMA200_BELOW_MAX_TRADES = 0  # Block all trades below EMA200
    config.USE_OPTIMIZED_RULES_ONLY = True

    try:
        print(f"   📊 Loading {config.CURRENT_DATASET_SIZE:,} candles...")
        engine = BacktestingEngine(config)

        # Check if we have valid EMA200 data
        if 'DAILY_EMA_200' not in engine.df.columns:
            print(f"   ❌ DAILY_EMA_200 column not found")
            return

        # Count valid EMA200 values
        valid_ema_count = engine.df['DAILY_EMA_200'].notna().sum()
        print(f"   📈 Valid EMA200 values: {valid_ema_count:,} out of {len(engine.df):,} candles")

        if valid_ema_count == 0:
            print(f"   ❌ No valid EMA200 data found")
            return

        # Find indices where we have valid EMA200 data
        valid_indices = engine.df[engine.df['DAILY_EMA_200'].notna()].index.tolist()

        if len(valid_indices) < 100:
            print(f"   ❌ Insufficient valid EMA200 data ({len(valid_indices)} points)")
            return

        # Test filter logic on sample of valid indices
        test_sample = valid_indices[50:150]  # Use middle section to avoid edge effects

        below_ema_count = 0
        above_ema_count = 0
        blocked_below_count = 0
        allowed_above_count = 0

        print(f"   🔍 Testing filter on {len(test_sample)} sample points...")

        for idx in test_sample:
            current_price = engine.df['close'].iloc[idx]
            ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]

            # Test the filter
            filter_result = engine._check_global_market_filters(idx)

            if current_price < ema200_value:
                below_ema_count += 1
                if not filter_result:
                    blocked_below_count += 1
            else:
                above_ema_count += 1
                if filter_result:
                    allowed_above_count += 1

        print(f"   📊 Results:")
        print(f"     - Price below EMA200: {below_ema_count} cases")
        print(f"     - Price above EMA200: {above_ema_count} cases")
        print(f"     - Blocked when below EMA200: {blocked_below_count}/{below_ema_count}")
        print(f"     - Allowed when above EMA200: {allowed_above_count}/{above_ema_count}")

        # Validate results
        if below_ema_count > 0:
            block_rate = (blocked_below_count / below_ema_count) * 100
            print(f"     - Block rate when below EMA200: {block_rate:.1f}%")

            if block_rate > 90:  # Should block most/all trades when max_trades=0
                print(f"   ✅ SUCCESS: Filter is blocking trades when below EMA200")
            else:
                print(f"   ❌ ISSUE: Filter should block more trades when max_trades=0")

        if above_ema_count > 0:
            allow_rate = (allowed_above_count / above_ema_count) * 100
            print(f"     - Allow rate when above EMA200: {allow_rate:.1f}%")

            if allow_rate > 90:  # Should allow most trades when above EMA200
                print(f"   ✅ SUCCESS: Filter allows trades when above EMA200")
            else:
                print(f"   ❌ ISSUE: Filter should allow trades when above EMA200")

    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_direct_filter_function():
    """Test the filter function directly with mock data"""
    print(f"\n🧪 TESTING FILTER FUNCTION DIRECTLY")
    print("=" * 60)

    # Create mock data
    mock_data = pd.DataFrame({
        'open': [100, 101, 102, 103, 104],
        'high': [105, 106, 107, 108, 109],
        'low': [95, 96, 97, 98, 99],
        'close': [102, 103, 104, 105, 106],  # Prices
        'volume': [1000, 1100, 1200, 1300, 1400],
        'DAILY_EMA_200': [110, 110, 110, 110, 110]  # EMA200 higher than prices (below EMA)
    })

    test_configs = [
        {"max_trades": 0, "expected_blocked": True},
        {"max_trades": 1, "expected_blocked": False},  # Should allow first trade
        {"max_trades": 2, "expected_blocked": False}   # Should allow first trade
    ]

    for test_config in test_configs:
        print(f"\n   📊 Testing max_trades = {test_config['max_trades']}")

        worker_config = {
            'ENABLE_GLOBAL_MARKET_FILTERS': True,
            'GLOBAL_EMA200_FILTER': True,
            'GLOBAL_EMA200_BELOW_MAX_TRADES': test_config['max_trades'],
        }

        # Test with no existing positions
        result = check_global_market_filters_worker(mock_data, 0, worker_config, [], [])
        expected = not test_config['expected_blocked']

        if result == expected:
            print(f"     ✅ CORRECT: Filter returned {result} (expected {expected})")
        else:
            print(f"     ❌ ERROR: Filter returned {result} (expected {expected})")

        # Test with existing positions below EMA200
        if test_config['max_trades'] > 0:
            mock_positions = []
            for i in range(test_config['max_trades']):
                mock_positions.append({
                    'entry_price': 105,  # Below EMA200 (110)
                    'entry_idx': 0,
                    'quantity': 100,
                    'position_value': 1000
                })

            # Should block when limit reached
            result_with_positions = check_global_market_filters_worker(mock_data, 1, worker_config, [], mock_positions)
            print(f"     📋 With {len(mock_positions)} existing positions below EMA200: {'✅ ALLOWED' if result_with_positions else '❌ BLOCKED'}")

            if not result_with_positions:
                print(f"     ✅ CORRECT: Blocked additional trade when limit reached")
            else:
                print(f"     ❌ ERROR: Should block when max_trades limit reached")

if __name__ == "__main__":
    try:
        test_ema200_filter_logic()
        test_filter_with_larger_dataset()
        test_direct_filter_function()

        print(f"\n🎉 EMA200 FILTER TEST COMPLETED!")
        print(f"   Check the results above to verify the filter is working correctly")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
