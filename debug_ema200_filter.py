#!/usr/bin/env python3
"""
Debug script to investigate why EMA200 filter isn't working
"""

from backtesting_engine import BacktestingEngine, check_global_market_filters_worker
from config import FullAnalysisConfig
import pandas as pd

def debug_ema200_filter():
    """Debug the EMA200 filter to find why it's not working"""
    print("🔍 DEBUGGING EMA200 FILTER")
    print("=" * 50)

    # Use the same configuration as 'python main.py full'
    config = FullAnalysisConfig()
    config.USE_OPTIMIZED_RULES_ONLY = True

    print(f"Using FullAnalysisConfig:")
    print(f"   - CURRENT_DATASET_SIZE: {config.CURRENT_DATASET_SIZE}")
    print(f"   - ENABLE_GLOBAL_MARKET_FILTERS: {config.ENABLE_GLOBAL_MARKET_FILTERS}")
    print(f"   - GLOBAL_EMA200_FILTER: {config.GLOBAL_EMA200_FILTER}")
    print(f"   - GLOBAL_EMA200_BELOW_MAX_TRADES: {config.GLOBAL_EMA200_BELOW_MAX_TRADES}")
    

    
    try:
        engine = BacktestingEngine(config)
        print(f"\n✅ Engine initialized successfully")
        print(f"   - Dataset size: {len(engine.df):,} candles")
        
        # Check if DAILY_EMA_200 column exists
        has_daily_ema = 'DAILY_EMA_200' in engine.df.columns
        print(f"   - DAILY_EMA_200 column exists: {has_daily_ema}")
        
        if has_daily_ema:
            # Check EMA200 data quality
            ema200_values = engine.df['DAILY_EMA_200'].dropna()
            print(f"   - DAILY_EMA_200 valid values: {len(ema200_values):,}")
            print(f"   - DAILY_EMA_200 range: ${ema200_values.min():.0f} - ${ema200_values.max():.0f}")
            
            # Find indices where EMA200 data is available
            valid_ema_indices = engine.df[engine.df['DAILY_EMA_200'].notna()].index.tolist()
            if len(valid_ema_indices) > 0:
                print(f"   - First valid EMA200 index: {valid_ema_indices[0]:,}")
                print(f"   - Last valid EMA200 index: {valid_ema_indices[-1]:,}")
            else:
                print(f"   ❌ No valid EMA200 data found!")
                return
            
            # Test the filter at various points
            print(f"\n🧪 Testing filter at sample indices...")
            test_indices = [
                valid_ema_indices[0],  # First valid
                valid_ema_indices[len(valid_ema_indices)//4],  # 25%
                valid_ema_indices[len(valid_ema_indices)//2],  # 50%
                valid_ema_indices[3*len(valid_ema_indices)//4],  # 75%
                valid_ema_indices[-1]  # Last valid
            ]
            
            below_ema_count = 0
            above_ema_count = 0
            blocked_count = 0
            allowed_count = 0
            
            for idx in test_indices:
                current_price = engine.df['close'].iloc[idx]
                ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
                below_ema = current_price < ema200_value
                
                # Test main engine filter
                main_filter_result = engine._check_global_market_filters(idx)
                
                # Test worker filter (simulate worker data)
                essential_columns = ['open', 'high', 'low', 'close', 'volume', 'DAILY_EMA_200']
                worker_df = engine.df[essential_columns].iloc[0:len(engine.df)]
                
                worker_config = {
                    'ENABLE_GLOBAL_MARKET_FILTERS': True,
                    'GLOBAL_EMA200_FILTER': True,
                    'GLOBAL_EMA200_BELOW_MAX_TRADES': 0,
                }
                
                worker_filter_result = check_global_market_filters_worker(worker_df, idx, worker_config)
                
                print(f"\n   Index {idx:,}:")
                print(f"     Price: ${current_price:.0f}")
                print(f"     EMA200: ${ema200_value:.0f}")
                print(f"     Below EMA200: {below_ema}")
                print(f"     Main Engine Filter: {'✅ ALLOWED' if main_filter_result else '❌ BLOCKED'}")
                print(f"     Worker Filter: {'✅ ALLOWED' if worker_filter_result else '❌ BLOCKED'}")
                print(f"     Filters Match: {'✅' if main_filter_result == worker_filter_result else '❌ MISMATCH!'}")
                
                if below_ema:
                    below_ema_count += 1
                else:
                    above_ema_count += 1
                    
                if worker_filter_result:
                    allowed_count += 1
                else:
                    blocked_count += 1
            
            print(f"\n📊 Summary:")
            print(f"   - Price below EMA200: {below_ema_count}/{len(test_indices)} samples")
            print(f"   - Price above EMA200: {above_ema_count}/{len(test_indices)} samples")
            print(f"   - Trades allowed: {allowed_count}/{len(test_indices)} samples")
            print(f"   - Trades blocked: {blocked_count}/{len(test_indices)} samples")
            
            # Check if filter is working as expected
            if config.GLOBAL_EMA200_BELOW_MAX_TRADES == 0:
                expected_blocked = below_ema_count
                if blocked_count == expected_blocked:
                    print(f"✅ Filter working correctly: {blocked_count} blocked when below EMA200")
                else:
                    print(f"❌ Filter NOT working: Expected {expected_blocked} blocked, got {blocked_count}")
            
            # Test a larger sample to see the overall pattern
            print(f"\n🔍 Testing larger sample (1000 random indices)...")
            import random
            random.seed(42)
            large_sample = random.sample(valid_ema_indices, min(1000, len(valid_ema_indices)))
            
            large_below_count = 0
            large_blocked_count = 0
            
            for idx in large_sample:
                current_price = engine.df['close'].iloc[idx]
                ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
                below_ema = current_price < ema200_value
                
                worker_filter_result = check_global_market_filters_worker(worker_df, idx, worker_config)
                
                if below_ema:
                    large_below_count += 1
                if not worker_filter_result:
                    large_blocked_count += 1
            
            print(f"   - Large sample: {large_below_count}/{len(large_sample)} below EMA200")
            print(f"   - Large sample: {large_blocked_count}/{len(large_sample)} blocked")
            print(f"   - Expected blocks: {large_below_count} (if filter working)")
            print(f"   - Actual blocks: {large_blocked_count}")
            
            if large_blocked_count == large_below_count:
                print(f"✅ Large sample test PASSED: Filter working correctly")
            else:
                print(f"❌ Large sample test FAILED: Filter not working correctly")
                print(f"   Difference: {abs(large_blocked_count - large_below_count)} trades")
        
        else:
            print(f"❌ DAILY_EMA_200 column not found in dataframe!")
            print(f"   Available columns: {list(engine.df.columns)}")
    
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_ema200_filter()
