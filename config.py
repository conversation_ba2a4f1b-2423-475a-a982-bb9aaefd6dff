"""
Configuration Module
Central configuration for comprehensive trading rule analysis
"""

import os
from datetime import datetime


class Config:
    """Central configuration for trading rule analysis system"""
    
    # Data Configuration
    DATA_FILE = 'selected_data.csv'
    
    # Dataset Size Configuration
    QUICK_TEST_SIZE = 10000       # 10k candles for quick testing (insufficient for EMA200)
    FULL_DATASET_SIZE = None      # Use full dataset (800k+ candles needed for reliable EMA200)

    # EMA200 Requirements
    MIN_EMA200_DATASET_SIZE = 288000  # Minimum 288k candles (200 days) for reliable daily EMA200
    
    # Current dataset size (change this to switch between quick/full)
    CURRENT_DATASET_SIZE = QUICK_TEST_SIZE
    
    # Capital and Position Configuration
    INITIAL_CAPITAL = 100000      # $100,000 starting capital
    POSITION_SIZE_PCT = 1.0       # 100% of capital per trade (for rule comparison)
    
    # Risk Management Configuration (SIMPLIFIED FOR SPEED)
    STOP_LOSS_PCT = 1.3           # 1.3% stop loss for long positions
    TAKE_PROFIT_PCT = 0.75        # 0.75% take profit for long positions
    MAX_HOLDING_PERIOD = None     # No time-based exit (None = disabled)

    # Futures Trading Configuration
    ENABLE_FUTURES_TRADING = False    # Enable futures trading with long/short positions
    ENABLE_SHORT_POSITIONS = False    # Enable short position entries
    ENABLE_LONG_POSITIONS = True      # Enable long position entries (can be deactivated)

    # Short Position Risk Management (Inverted from long positions)
    SHORT_STOP_LOSS_PCT = 1.3         # +1.3% stop loss for short positions (price rises)
    SHORT_TAKE_PROFIT_PCT = 0.75      # -0.75% take profit for short positions (price falls)

    # Futures Leverage Configuration
    FUTURES_LEVERAGE = 10              # 10x leverage for futures positions

    # Independent Sell Rule Evaluation (like buy rules)
    ENABLE_INDEPENDENT_SELL_EVALUATION = False  # Evaluate each sell rule independently
    PREVENT_OVERLAPPING_SELL_POSITIONS = True   # Prevent same rule from opening multiple positions

    # Market Regime Detection
    ENABLE_MARKET_REGIME_DETECTION = False  # Enable automatic bull/bear market detection
    MARKET_REGIME_LOOKBACK = 50             # Periods to look back for trend detection
    BULL_MARKET_THRESHOLD = 0.02            # 2% uptrend over lookback period = bull market
    BEAR_MARKET_THRESHOLD = -0.02           # -2% downtrend over lookback period = bear market

    # Buy/Sell Rule Interaction Configuration
    BLOCK_BUY_IF_SELL_ACTIVE = None   # None/False = allow buys regardless of sell rules
                                      # True = block buy entries if sell rule is active
                                      # Useful for isolating buy rule performance

    # Filter Rules Configuration
    ENABLE_FILTER_RULES = True        # Enable filter rules to block trades
                                      # When True, the 18 filter rules will block buy signals when active
                                      # When False, filter rules are ignored

    # Global Market Filter Configuration
    ENABLE_GLOBAL_MARKET_FILTERS = True    # Enable global market condition filters

    # NEW EMA200 Filter - Properly implemented with daily candle conversion
    # IMPORTANT: Requires minimum 288,000 candles (200 days * 1440 minutes) for reliable EMA200
    GLOBAL_EMA200_FILTER = True            # Enable EMA200 filter (uses proper daily candles)
    GLOBAL_EMA200_BELOW_MAX_TRADES = 0     # Max concurrent trades when price below EMA200 (0 = no trades, 1 = max 1 trade)
    GLOBAL_RSI_OVERBOUGHT_FILTER = True    # Block buys when RSI > 75 (overbought)
    GLOBAL_RSI_OVERBOUGHT_THRESHOLD = 75   # RSI threshold for overbought condition

    # Global Market Structure Filters
    GLOBAL_MA_WATERFALL_FILTER = False    # Enable MA50 > MA25 filter (daily timeframe)
    GLOBAL_MA_WATERFALL_MAX_TRADES = 0    # Max trades when MA50 > MA25 (0 = no trades, 1 = one trade)



    # Sell Rules Control
    DISABLE_ALL_SELL_RULES = True    # False = use sell rules normally
                                      # True = disable all sell rules, use only SL/TP for exits
                                      # Useful for testing pure SL/TP performance
    
    # SL/TP Grid Search Configuration
    # If ENABLE_SLTP_GRID_SEARCH is True, run all RISK_CONFIGS automatically
    # If False, use only STOP_LOSS_PCT and TAKE_PROFIT_PCT above
    ENABLE_SLTP_GRID_SEARCH = False   # Set to True for comprehensive SL/TP testing

    RISK_CONFIGS = [
       # {'stop_loss': 1.5, 'take_profit': 0.75, 'name': 'Ultra_Conservative'},
        {'stop_loss': 1.25, 'take_profit': 0.75, 'name': 'Previous_Default'},
        {'stop_loss': 1.25, 'take_profit': 1.0, 'name': 'Moderate'},
        {'stop_loss': 1.3, 'take_profit': 0.75, 'name': 'Current_Default'},
       # {'stop_loss': 2.5, 'take_profit': 1.5, 'name': 'Tight_1_1.5'},
    ]

    # Quick SL/TP configs for faster testing
    QUICK_RISK_CONFIGS = [
        {'stop_loss': 1.5, 'take_profit': 0.75, 'name': 'Ultra_Conservative'},
        {'stop_loss': 2.25, 'take_profit': 1.0, 'name': 'Current_Default'},
        {'stop_loss': 1.25, 'take_profit': 1.25, 'name': 'Moderate'},
    ]

    # Dynamic Stop-Loss Configuration
    ENABLE_DYNAMIC_SL = True
    DYNAMIC_SL_TRIGGER = 0.05      # Move SL when price is +0.05% above entry
    DYNAMIC_SL_TIGHTENING = 0.05   # Tighten SL by 0.05%
    TRAIL_ON_TP_HIT = True         # Trail SL when TP hit but signals still active
    TRAIL_BUFFER = 0.05            # Buffer below TP for trailing SL

    # SL/TP Optimization Settings
    ENABLE_SLTP_OPTIMIZATION = True
    OPTIMIZATION_MODE = 'comprehensive'  # 'quick', 'comprehensive', 'custom'
    
    # Backtest Range Configuration
    START_IDX = 0                 # Start from beginning, warmup handled separately
    END_IDX = None                # None = use CURRENT_DATASET_SIZE
    
    # Rule Performance Filtering Thresholds (RELAXED FOR BETTER RULE DISCOVERY)
    BUY_RULE_FILTERS = {
        'min_win_rate': 35.0,         # Minimum 35% win rate (relaxed from 45%)
        'min_profit_factor': 1.0,     # Minimum 1.0 profit factor (relaxed from 1.1)
        'min_trades': 30,             # Minimum 30 trades (relaxed from 50)
        'max_avg_loss': -10.0,        # Maximum 10% average loss (relaxed from -5%)
        'min_sharpe_ratio': 0.0,      # Minimum Sharpe ratio (relaxed from 0.5)
        'min_total_return': 5.0,      # NEW: Minimum 5% total return
    }
    
    SELL_RULE_FILTERS = {
        'min_accuracy': 50.0,         # Minimum 50% accuracy (relaxed from 60%)
        'min_avg_decline': 1.5,       # Minimum 1.5% decline (relaxed from 2.0%)
        'min_signals': 20,            # Minimum 20 signals (relaxed from 30)
        'min_hit_rate': 0.5,          # Minimum 50% hit rate (relaxed from 60%)
    }
    
    # Rule Categories Configuration
    RULE_CATEGORIES = {
        'ORIGINAL': 'Original rules from previous analysis',
        'AI_GENERATED': 'AI-generated rules',
        'ACADEMIC': 'Rules from academic research',
        'PROFESSIONAL': 'Professional/institutional rules',
        'MOMENTUM': 'Momentum-based strategies',
        'MEAN_REVERSION': 'Mean reversion strategies',
        'BREAKOUT': 'Breakout strategies',
        'VOLUME': 'Volume-based strategies',
    }
    
    # Rule Enable/Disable Configuration
    ENABLED_RULE_CATEGORIES = [
        'ORIGINAL',
        'AI_GENERATED',
        'ACADEMIC',
        'PROFESSIONAL',
        'MOMENTUM',
        'MEAN_REVERSION',
        'BREAKOUT',
        'VOLUME',
        'EXPANDED',      # NEW: Expanded professional rules
    ]
    
    # Individual Rule Control (can disable specific rules)
    DISABLED_RULES = [
        # Add rule names here to disable them
        # Example: 'Rule 1: Simple MA Cross'
    ]
    
    # Performance Tracking Configuration
    TRACK_INDIVIDUAL_RULES = True    # Track each rule separately
    TRACK_RULE_COMBINATIONS = False  # Track rule combinations (expensive)
    SAVE_ALL_SIGNALS = True          # Save all signals, not just trades

    # Evaluation Method Configuration
    USE_UNIFIED_EVALUATION = True    # NEW: Use unified evaluation (all rules together)
    USE_INDEPENDENT_EVALUATION = False  # NEW: Use independent evaluation (each rule isolated)
    COMPARE_WITH_INDIVIDUAL = False  # Also run individual tests for comparison

    # Concurrent Trades Configuration
    MAX_CONCURRENT_TRADES = 1        # Maximum number of concurrent trades (1 = traditional single position)
                                     # Set higher (e.g., 5) to allow multiple simultaneous positions

    # Performance Optimization Configuration
    USE_MULTIPROCESSING = True       # Enable multiprocessing for faster execution
    MAX_WORKERS = None              # Auto-detect optimal worker count (None = auto)
    
    # Output Configuration
    OUTPUT_DIR = 'results'
    SAVE_DETAILED_LOGS = True
    SAVE_CHARTS = True
    SAVE_RULE_ANALYSIS = True
    
    # Progress Reporting
    PROGRESS_INTERVAL = 10000        # Print progress every N candles
    VERBOSE_LOGGING = True
    
    # Technical Indicator Configuration
    INDICATOR_PERIODS = {
        'SMA_SHORT': 20,
        'SMA_MEDIUM': 50,
        'SMA_LONG': 200,
        'EMA_SHORT': 12,
        'EMA_MEDIUM': 26,
        'EMA_LONG': 50,
        'RSI_PERIOD': 14,
        'STOCH_PERIOD': 14,
        'MACD_FAST': 12,
        'MACD_SLOW': 26,
        'MACD_SIGNAL': 9,
        'BB_PERIOD': 20,
        'BB_STD': 2,
        'ATR_PERIOD': 14,
        'VOLUME_MA_PERIOD': 20,
    }

    # Trading Pause After Loss
    ENABLE_TRADING_PAUSE_AFTER_LOSS = True    # Pause trading after significant losses
    TRADING_PAUSE_LOSS_THRESHOLD = 3.9        # Pause if loss exceeds 3.9% in the lookback period
    TRADING_PAUSE_LOOKBACK_PERIOD = 1440      # Look back 24 hours (1440 minutes) for losses
    TRADING_PAUSE_DURATION = 1440             # Pause trading for 24 hours after threshold is hit

    # Consecutive Losses Protection
    ENABLE_CONSECUTIVE_LOSSES_FILTER = True   # Stop trading after consecutive losses
    CONSECUTIVE_LOSSES_THRESHOLD = 5          # Stop trading after 5 consecutive losses
    CONSECUTIVE_LOSSES_PAUSE_DURATION = 1440  # Pause trading for 24 hours after consecutive losses

    # Data Warmup Period (for proper indicator calculation)
    DATA_WARMUP_PERIOD = 288000               # Skip first 288k candles (warmup period for daily EMA200)
    ENABLE_DATA_WARMUP = True                # Set to True when using extended dataset
    
    @classmethod
    def get_dataset_size(cls):
        """Get current dataset size"""
        return cls.CURRENT_DATASET_SIZE
    
    @classmethod
    def set_quick_test(cls):
        """Set configuration for quick testing"""
        cls.CURRENT_DATASET_SIZE = cls.QUICK_TEST_SIZE
        cls.PROGRESS_INTERVAL = 5000
        cls.SAVE_CHARTS = False
        print(f"Configuration set to QUICK TEST mode ({cls.QUICK_TEST_SIZE:,} candles)")
    
    @classmethod
    def set_full_dataset(cls):
        """Set configuration for full dataset analysis"""
        cls.CURRENT_DATASET_SIZE = cls.FULL_DATASET_SIZE
        cls.PROGRESS_INTERVAL = 50000
        cls.SAVE_CHARTS = True
        print(f"Configuration set to FULL DATASET mode")
    
    @classmethod
    def set_custom_size(cls, size):
        """Set custom dataset size"""
        cls.CURRENT_DATASET_SIZE = size
        print(f"Configuration set to CUSTOM mode ({size:,} candles)")
    
    def get_backtest_range(self, total_length):
        """Get backtest range based on configuration"""
        start_idx = self.START_IDX

        # Apply warmup period if enabled
        if getattr(self, 'ENABLE_DATA_WARMUP', True):
            warmup_period = getattr(self, 'DATA_WARMUP_PERIOD', 0)
            start_idx = max(start_idx, warmup_period)
            print(f"📊 Data warmup enabled: Skipping first {warmup_period:,} candles for indicator calculation")
            print(f"   Trading will start from index {start_idx:,}")

        if self.END_IDX is not None:
            end_idx = min(self.END_IDX, total_length)
        elif self.CURRENT_DATASET_SIZE is not None:
            end_idx = min(start_idx + self.CURRENT_DATASET_SIZE, total_length)
        else:
            end_idx = total_length

        return start_idx, end_idx
    
    @classmethod
    def is_rule_enabled(cls, rule_name, category=None):
        """Check if a rule is enabled"""
        # Check if rule is specifically disabled
        if rule_name in cls.DISABLED_RULES:
            return False
        
        # Check if category is enabled
        if category and category not in cls.ENABLED_RULE_CATEGORIES:
            return False
        
        return True
    
    @classmethod
    def ensure_output_dir(cls):
        """Ensure output directory exists"""
        if not os.path.exists(cls.OUTPUT_DIR):
            os.makedirs(cls.OUTPUT_DIR)
    
    @classmethod
    def get_output_filename(cls, base_name, extension='csv'):
        """Generate output filename with timestamp"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{base_name}_{timestamp}.{extension}"
        return os.path.join(cls.OUTPUT_DIR, filename)

    @classmethod
    def get_risk_configs(cls):
        """Get risk configurations for SL/TP testing"""
        if cls.ENABLE_SLTP_GRID_SEARCH:
            # Use appropriate config set based on dataset size
            if cls.CURRENT_DATASET_SIZE == cls.QUICK_TEST_SIZE:
                return cls.QUICK_RISK_CONFIGS
            else:
                return cls.RISK_CONFIGS
        else:
            # Single configuration using current SL/TP settings
            return [{'stop_loss': cls.STOP_LOSS_PCT, 'take_profit': cls.TAKE_PROFIT_PCT, 'name': 'Current_Settings'}]


# Preset configurations for different use cases
class QuickTestConfig(Config):
    """Configuration for quick testing and development"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = False
    SAVE_DETAILED_LOGS = False
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Default: allow buys regardless of sell rules
    ENABLE_SLTP_GRID_SEARCH = False      # Default: single SL/TP configuration
    DISABLE_ALL_SELL_RULES = True       # Default: use sell rules normally


class FullAnalysisConfig(Config):
    """Configuration for full dataset analysis"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = False           # Disable detailed logs for speed
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Not applicable for independent evaluation
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True        # Use only SL/TP for exits
    USE_UNIFIED_EVALUATION = True        # Enable unified evaluation with multiple concurrent trades
    USE_INDEPENDENT_EVALUATION = False   # Disable independent evaluation
    MAX_CONCURRENT_TRADES = 2            # Maximum 2 open trades simultaneously

    # NEW EMA200 Filter Configuration - Ensure sufficient data
    # CRITICAL: EMA200 requires minimum 288,000 candles (200 days * 1440 minutes/day)
    # The full dataset should have 800k+ candles to ensure reliable EMA200 calculation
    GLOBAL_EMA200_FILTER = True          # Enable NEW EMA200 filter with proper daily conversion
    GLOBAL_EMA200_BELOW_MAX_TRADES = 0   # Block ALL trades when price below EMA200 (for testing)
    USE_MULTIPROCESSING = True           # Enable multiprocessing for maximum speed
    MAX_WORKERS = 8           # 4m veri seti için 4 yap           # MEMORY OPTIMIZED: Balanced for 4M dataset without memory overflow

    # EXTREME optimization settings (same as IndependentEvaluationConfig)
    ULTRA_FAST_MODE = False              # Test all rules
    SAMPLE_RULES_ONLY = 142              # Test all rules
    USE_VECTORIZED_SIGNALS = False       # Disable vectorized signals to use pure multiprocessing
    ENABLE_GLOBAL_MARKET_FILTERS = True     # Enable global market condition filters (required for daily drop filter)
    # GLOBAL_EMA200_FILTER = True            # Block buys when price is below EMA200 (REMOVED - defined above)
    GLOBAL_RSI_OVERBOUGHT_FILTER = False    # Block buys when RSI > 75 (overbought)
    GLOBAL_RSI_OVERBOUGHT_THRESHOLD = 75 # RSI threshold for overbought condition (daily RSI)

    # New Global Market Structure Filters
    GLOBAL_MA_WATERFALL_FILTER = False       # Enable MA7 < MA25 < MA50 waterfall filter (daily timeframe)
    GLOBAL_MA_WATERFALL_MAX_TRADES = 0      # Max trades when waterfall active (0 = no trades, 1 = one trade)
    # GLOBAL_EMA200_BELOW_MAX_TRADES defined above in NEW EMA200 Filter Configuration


    
    # Use optimized rules only
    USE_OPTIMIZED_RULES_ONLY = True
    
    # Trading Pause After Loss
    ENABLE_TRADING_PAUSE_AFTER_LOSS = False    # Pause trading after significant losses
    TRADING_PAUSE_LOSS_THRESHOLD = 5.0        # Pause if loss exceeds 3.9% in the lookback period
    TRADING_PAUSE_LOOKBACK_PERIOD = 1440      # Look back 24 hours (1440 minutes) for losses
    TRADING_PAUSE_DURATION = 1440             # Pause trading for 24 hours after threshold is hit

    # Consecutive Losses Protection
    ENABLE_CONSECUTIVE_LOSSES_FILTER = False   # Stop trading after consecutive losses
    CONSECUTIVE_LOSSES_THRESHOLD = 3          # Stop trading after 5 consecutive losses
    CONSECUTIVE_LOSSES_PAUSE_DURATION = 1440  # Pause trading for 24 hours after consecutive losses

    # Data Warmup Period (needed for proper daily EMA200 calculation)
    DATA_WARMUP_PERIOD = 288000               # Need 200 days = 288k minutes for daily EMA200
    ENABLE_DATA_WARMUP = True                 # Enable warmup period for proper daily indicators
    START_IDX = 0                             # Start from beginning, warmup handled separately


class ResearchConfig(Config):
    """Configuration for research and rule development"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = True
    SAVE_ALL_SIGNALS = True
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Default: allow buys regardless of sell rules
    ENABLE_SLTP_GRID_SEARCH = True       # Enable grid search for research
    DISABLE_ALL_SELL_RULES = True       # Default: use sell rules normally


# New specialized configurations
class IsolatedBuyRulesConfig(Config):
    """Configuration for testing buy rules in isolation (no sell rule interference)"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = True      # Block buys when sell rules are active
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True       # Default: use sell rules normally


class GridSearchConfig(Config):
    """Configuration for comprehensive SL/TP grid search"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Default behavior
    ENABLE_SLTP_GRID_SEARCH = True       # Enable comprehensive grid search
    DISABLE_ALL_SELL_RULES = True       # Default: use sell rules normally


class SLTPOnlyConfig(Config):
    """Configuration for testing pure SL/TP performance (no sell rules)"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Default behavior
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True        # Disable all sell rules - use only SL/TP


class FullAnalysisSLTPOnlyConfig(Config):
    """Configuration for full dataset analysis with only SL/TP exits (no sell rules)"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Default: allow buys regardless of sell rules
    ENABLE_SLTP_GRID_SEARCH = False      # Default: single SL/TP configuration
    DISABLE_ALL_SELL_RULES = True        # Disable all sell rules - use only SL/TP


class IndependentEvaluationConfig(Config):
    """Configuration for independent buy rule evaluation - ULTRA OPTIMIZED"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE  # Back to 10k for real test
    PROGRESS_INTERVAL = 100              # Frequent progress updates
    SAVE_CHARTS = True                   # Re-enable charts
    SAVE_DETAILED_LOGS = False           # Keep logs disabled for speed
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Not applicable for independent evaluation
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True        # Use only SL/TP for exits
    USE_UNIFIED_EVALUATION = False       # Disable unified evaluation
    USE_INDEPENDENT_EVALUATION = True    # Enable independent evaluation
    USE_MULTIPROCESSING = True           # Enable multiprocessing for speed
    MAX_WORKERS = 8                     # EXTREME SPEED: Massive oversubscription for maximum throughput

    # Ultra optimization settings
    ULTRA_FAST_MODE = False              # Test all rules now
    SAMPLE_RULES_ONLY = 142              # Test all rules
    USE_VECTORIZED_SIGNALS = False       # Disable vectorized signals to use pure multiprocessing


class FullIndependentEvaluationConfig(Config):
    """Configuration for independent buy rule evaluation on full dataset - EXTREME SPEED"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = False           # Disable detailed logs for speed
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Not applicable for independent evaluation
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True        # Use only SL/TP for exits
    USE_UNIFIED_EVALUATION = False       # Disable unified evaluation
    USE_INDEPENDENT_EVALUATION = True    # Enable independent evaluation
    USE_MULTIPROCESSING = True           # Enable TRUE multiprocessing for maximum speed
    MAX_WORKERS = 8           # 4m veri seti için 4 yap           # MEMORY OPTIMIZED: Balanced for 4M dataset without memory overflow

    # EXTREME optimization settings (same as IndependentEvaluationConfig)
    ULTRA_FAST_MODE = False              # Test all rules
    SAMPLE_RULES_ONLY = 142              # Test all rules
    USE_VECTORIZED_SIGNALS = False       # Disable vectorized signals to use pure multiprocessing
    ENABLE_GLOBAL_MARKET_FILTERS = False    # Enable global market condition filters
    # GLOBAL_EMA200_FILTER = False            # Block buys when price is below EMA200 (REMOVED - defined above)
    GLOBAL_RSI_OVERBOUGHT_FILTER = False    # Block buys when RSI > 75 (overbought)
    GLOBAL_RSI_OVERBOUGHT_THRESHOLD = 75   # RSI threshold for overbought condition

    # Use optimized rules only
    USE_OPTIMIZED_RULES_ONLY = True



# Futures Trading Configurations
class FuturesLongShortConfig(Config):
    """Configuration for futures trading with both long and short positions"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False

    # Enable futures trading
    ENABLE_FUTURES_TRADING = True
    ENABLE_SHORT_POSITIONS = True
    ENABLE_MARKET_REGIME_DETECTION = True

    # Risk management for both directions
    STOP_LOSS_PCT = 1.25              # Long position stop loss
    TAKE_PROFIT_PCT = 0.75            # Long position take profit
    SHORT_STOP_LOSS_PCT = 1.25        # Short position stop loss (inverted)
    SHORT_TAKE_PROFIT_PCT = 0.75      # Short position take profit (inverted)

    # Dynamic stop-loss for both directions
    ENABLE_DYNAMIC_SL = True
    TRAIL_ON_TP_HIT = True
    TRAIL_BUFFER = 0.05

    # Use sell rules for short entries
    DISABLE_ALL_SELL_RULES = False


class FullFuturesConfig(Config):
    """Configuration for full dataset futures trading analysis"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False

    # Enable futures trading
    ENABLE_FUTURES_TRADING = True
    ENABLE_SHORT_POSITIONS = True
    ENABLE_MARKET_REGIME_DETECTION = True

    # Risk management for both directions
    STOP_LOSS_PCT = 1.25              # Long position stop loss
    TAKE_PROFIT_PCT = 0.75            # Long position take profit
    SHORT_STOP_LOSS_PCT = 1.25        # Short position stop loss (inverted)
    SHORT_TAKE_PROFIT_PCT = 0.75      # Short position take profit (inverted)

    # Dynamic stop-loss for both directions
    ENABLE_DYNAMIC_SL = True
    TRAIL_ON_TP_HIT = True
    TRAIL_BUFFER = 0.05

    # Use sell rules for short entries
    DISABLE_ALL_SELL_RULES = False


class ShortOnlyFuturesConfig(Config):
    """Configuration for short-only futures trading with independent sell rule evaluation"""
    CURRENT_DATASET_SIZE = Config.QUICK_TEST_SIZE
    PROGRESS_INTERVAL = 5000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False

    # Enable futures trading - SHORT ONLY
    ENABLE_FUTURES_TRADING = True
    ENABLE_SHORT_POSITIONS = True
    ENABLE_LONG_POSITIONS = False          # DEACTIVATED - no long positions

    # Independent sell rule evaluation (like buy rules)
    ENABLE_INDEPENDENT_SELL_EVALUATION = True
    PREVENT_OVERLAPPING_SELL_POSITIONS = True
    USE_INDEPENDENT_EVALUATION = True      # Use independent evaluation mode
    USE_UNIFIED_EVALUATION = False         # Disable unified evaluation

    # Futures leverage and risk management
    FUTURES_LEVERAGE = 10                  # 10x leverage
    SHORT_STOP_LOSS_PCT = 1.25            # +1.25% stop loss for shorts
    SHORT_TAKE_PROFIT_PCT = 0.75           # -0.75% take profit for shorts

    # Dynamic stop-loss for shorts
    ENABLE_DYNAMIC_SL = True
    TRAIL_ON_TP_HIT = True
    TRAIL_BUFFER = 0.05

    # Disable buy rules and long positions
    DISABLE_ALL_SELL_RULES = False         # Keep sell rules active

    # Performance optimization - ENABLED for parallel sell rule evaluation
    USE_MULTIPROCESSING = True
    MAX_WORKERS = 8

    # Batch processing (same as independent evaluation)
    BATCH_SIZE = 142                   # Fixed batch size for memory management


class FullShortOnlyFuturesConfig(Config):
    """Configuration for full dataset short-only futures trading"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = True
    TRACK_RULE_COMBINATIONS = False

    # Enable futures trading - SHORT ONLY
    ENABLE_FUTURES_TRADING = True
    ENABLE_SHORT_POSITIONS = True
    ENABLE_LONG_POSITIONS = False          # DEACTIVATED - no long positions

    # Independent sell rule evaluation (like buy rules)
    ENABLE_INDEPENDENT_SELL_EVALUATION = True
    PREVENT_OVERLAPPING_SELL_POSITIONS = True
    USE_INDEPENDENT_EVALUATION = True      # Use independent evaluation mode
    USE_UNIFIED_EVALUATION = False         # Disable unified evaluation

    # Futures leverage and risk management
    FUTURES_LEVERAGE = 10                  # 10x leverage
    SHORT_STOP_LOSS_PCT = 1.25            # +1.25% stop loss for shorts
    SHORT_TAKE_PROFIT_PCT = 0.75           # -0.75% take profit for shorts

    # Dynamic stop-loss for shorts
    ENABLE_DYNAMIC_SL = True
    TRAIL_ON_TP_HIT = True
    TRAIL_BUFFER = 0.05

    # Disable buy rules and long positions
    DISABLE_ALL_SELL_RULES = False         # Keep sell rules active

    # Performance optimization - ENABLED for parallel sell rule evaluation
    USE_MULTIPROCESSING = True
    MAX_WORKERS = 8

    # Batch processing (same as independent evaluation)
    BATCH_SIZE = 24                   # Fixed batch size for memory management


class OptimizedRulesConfig(Config):
    """Configuration for testing ONLY the optimized rules from selected_buy_rules_optimized.py"""

    # Use optimized rules only
    USE_OPTIMIZED_RULES_ONLY = True

    # Disable all rule categories since we're using optimized rules directly
    ENABLED_RULE_CATEGORIES = []  # Empty - we'll load optimized rules directly

    """Configuration for full dataset analysis"""
    CURRENT_DATASET_SIZE = Config.FULL_DATASET_SIZE
    PROGRESS_INTERVAL = 50000
    SAVE_CHARTS = True
    SAVE_DETAILED_LOGS = False           # Disable detailed logs for speed
    TRACK_RULE_COMBINATIONS = False
    BLOCK_BUY_IF_SELL_ACTIVE = None      # Not applicable for independent evaluation
    ENABLE_SLTP_GRID_SEARCH = False
    DISABLE_ALL_SELL_RULES = True        # Use only SL/TP for exits
    USE_UNIFIED_EVALUATION = False       # Disable unified evaluation
    USE_INDEPENDENT_EVALUATION = False    # Enable independent evaluation
    USE_MULTIPROCESSING = True           # Enable TRUE multiprocessing for maximum speed
    MAX_WORKERS = 8           # 4m veri seti için 4 yap           # MEMORY OPTIMIZED: Balanced for 4M dataset without memory overflow

    # EXTREME optimization settings (same as IndependentEvaluationConfig)
    ULTRA_FAST_MODE = False              # Test all rules
    USE_VECTORIZED_SIGNALS = False       # Disable vectorized signals to use pure multiprocessing
    ENABLE_GLOBAL_MARKET_FILTERS = True    # Enable global market condition filters
    # GLOBAL_EMA200_FILTER = False            # Block buys when price is below EMA200 (REMOVED - defined above)
    GLOBAL_RSI_OVERBOUGHT_FILTER = False    # Block buys when RSI > 75 (overbought)
    GLOBAL_RSI_OVERBOUGHT_THRESHOLD = 75   # RSI threshold for overbought condition

    # Relaxed filtering for initial evaluation
    BUY_RULE_FILTERS = {
        'min_win_rate': 30.0,         # Very relaxed for initial testing
        'min_profit_factor': 0.8,     # Very relaxed
        'min_trades': 20,             # Lower minimum
        'max_avg_loss': -15.0,        # More lenient
        'min_sharpe_ratio': 0.0,      # No minimum
        'min_total_return': 0.0,      # No minimum for initial test
    }


class OptimizedRulesFullConfig(OptimizedRulesConfig):
    """Configuration for full dataset testing of optimized rules"""
    CURRENT_DATASET_SIZE = None  # Use full dataset

    # More stringent filters for full dataset
    BUY_RULE_FILTERS = {
        'min_win_rate': 40.0,         # Higher standard for full dataset
        'min_profit_factor': 1.0,     # Minimum breakeven
        'min_trades': 50,             # More trades required
        'max_avg_loss': -10.0,        # Tighter loss control
        'min_sharpe_ratio': 0.2,      # Some risk-adjusted return requirement
        'min_total_return': 5.0,      # Minimum 5% total return
    }
