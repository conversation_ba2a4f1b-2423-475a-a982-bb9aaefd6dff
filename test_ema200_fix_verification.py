#!/usr/bin/env python3
"""
Quick test to verify the EMA200 filter fix is working
"""

import sys
import os
import pandas as pd

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine, check_ema200_filter_worker

def test_ema200_filter_fix():
    """Test that the EMA200 filter is actually working"""
    print("🔧 TESTING EMA200 FILTER FIX")
    print("=" * 50)
    
    # Use configuration that should block trades
    config = FullAnalysisConfig()
    config.GLOBAL_EMA200_FILTER = True
    config.GLOBAL_EMA200_BELOW_MAX_TRADES = 0  # Should block ALL trades below EMA200
    
    print(f"📊 Configuration:")
    print(f"   - GLOBAL_EMA200_FILTER: {config.GLOBAL_EMA200_FILTER}")
    print(f"   - GLOBAL_EMA200_BELOW_MAX_TRADES: {config.GLOBAL_EMA200_BELOW_MAX_TRADES}")
    
    try:
        # Initialize engine
        print(f"\n📈 Initializing engine...")
        engine = BacktestingEngine(config)
        print(f"   ✅ Engine initialized with {len(engine.df):,} candles")
        
        # Check EMA200 data
        if 'DAILY_EMA_200' not in engine.df.columns:
            print(f"   ❌ DAILY_EMA_200 not found")
            return
        
        valid_ema_data = engine.df[engine.df['DAILY_EMA_200'].notna()]
        print(f"   📈 Valid EMA200 data: {len(valid_ema_data):,} points")
        
        # Find points where price is below EMA200
        below_ema_data = valid_ema_data[valid_ema_data['close'] < valid_ema_data['DAILY_EMA_200']]
        above_ema_data = valid_ema_data[valid_ema_data['close'] >= valid_ema_data['DAILY_EMA_200']]
        
        print(f"   📉 Price below EMA200: {len(below_ema_data):,} points")
        print(f"   📈 Price above EMA200: {len(above_ema_data):,} points")
        
        if len(below_ema_data) == 0:
            print(f"   ❌ No data points below EMA200 - cannot test")
            return
        
        # Test the filter on points below EMA200
        print(f"\n🧪 Testing filter on points BELOW EMA200 (should be BLOCKED):")
        test_indices_below = below_ema_data.index[:10].tolist()  # Test first 10
        
        blocked_count = 0
        allowed_count = 0
        
        for idx in test_indices_below:
            # Test worker filter
            worker_config = {
                'ENABLE_GLOBAL_MARKET_FILTERS': True,
                'GLOBAL_EMA200_FILTER': True,
                'GLOBAL_EMA200_BELOW_MAX_TRADES': 0,
            }
            
            essential_columns = ['open', 'high', 'low', 'close', 'volume', 'DAILY_EMA_200']
            worker_df = engine.df[essential_columns]
            
            result = check_ema200_filter_worker(worker_df, idx, worker_config, [], [])
            
            current_price = engine.df['close'].iloc[idx]
            ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
            
            if result:
                allowed_count += 1
                status = "✅ ALLOWED"
            else:
                blocked_count += 1
                status = "❌ BLOCKED"
            
            print(f"   Index {idx}: Price ${current_price:.0f} < EMA200 ${ema200_value:.0f} → {status}")
        
        print(f"\n📊 Results for points BELOW EMA200:")
        print(f"   - Allowed: {allowed_count}")
        print(f"   - Blocked: {blocked_count}")
        
        if blocked_count == len(test_indices_below):
            print(f"   ✅ SUCCESS: ALL trades blocked when below EMA200 (as expected)")
        else:
            print(f"   ❌ FAILURE: Some trades allowed when they should be blocked")
        
        # Test the filter on points above EMA200
        if len(above_ema_data) > 0:
            print(f"\n🧪 Testing filter on points ABOVE EMA200 (should be ALLOWED):")
            test_indices_above = above_ema_data.index[:5].tolist()  # Test first 5
            
            blocked_count_above = 0
            allowed_count_above = 0
            
            for idx in test_indices_above:
                result = check_ema200_filter_worker(worker_df, idx, worker_config, [], [])
                
                current_price = engine.df['close'].iloc[idx]
                ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
                
                if result:
                    allowed_count_above += 1
                    status = "✅ ALLOWED"
                else:
                    blocked_count_above += 1
                    status = "❌ BLOCKED"
                
                print(f"   Index {idx}: Price ${current_price:.0f} > EMA200 ${ema200_value:.0f} → {status}")
            
            print(f"\n📊 Results for points ABOVE EMA200:")
            print(f"   - Allowed: {allowed_count_above}")
            print(f"   - Blocked: {blocked_count_above}")
            
            if allowed_count_above == len(test_indices_above):
                print(f"   ✅ SUCCESS: ALL trades allowed when above EMA200 (as expected)")
            else:
                print(f"   ❌ FAILURE: Some trades blocked when they should be allowed")
        
        # Test with max_trades = 1
        print(f"\n🧪 Testing with max_trades = 1 (should allow first trade below EMA200):")
        
        worker_config_1 = {
            'ENABLE_GLOBAL_MARKET_FILTERS': True,
            'GLOBAL_EMA200_FILTER': True,
            'GLOBAL_EMA200_BELOW_MAX_TRADES': 1,
        }
        
        # Test with no existing positions (should allow)
        test_idx = below_ema_data.index[0]
        result_no_pos = check_ema200_filter_worker(worker_df, test_idx, worker_config_1, [], [])
        print(f"   No existing positions: {'✅ ALLOWED' if result_no_pos else '❌ BLOCKED'}")
        
        # Test with 1 existing position below EMA200 (should block)
        mock_position = {
            'entry_price': engine.df['DAILY_EMA_200'].iloc[test_idx] * 0.95,  # Below EMA200
            'entry_idx': test_idx,
            'quantity': 100,
            'position_value': 1000
        }
        
        result_with_pos = check_ema200_filter_worker(worker_df, test_idx, worker_config_1, [], [mock_position])
        print(f"   With 1 position below EMA200: {'✅ ALLOWED' if result_with_pos else '❌ BLOCKED'}")
        
        if result_no_pos and not result_with_pos:
            print(f"   ✅ SUCCESS: max_trades=1 logic working correctly")
        else:
            print(f"   ❌ FAILURE: max_trades=1 logic not working")
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        test_ema200_filter_fix()
        
        print(f"\n🎯 CONCLUSION:")
        print("If you see 'SUCCESS' messages above, the EMA200 filter is working correctly.")
        print("If you see 'FAILURE' messages, there are still issues to fix.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
