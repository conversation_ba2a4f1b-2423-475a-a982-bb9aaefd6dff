#!/usr/bin/env python3
"""
Test EMA200 filter in real trading scenario
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine

def test_ema200_real_trading():
    """Test EMA200 filter with actual trading rules"""
    print("🚀 TESTING EMA200 FILTER IN REAL TRADING")
    print("=" * 50)
    
    # Test different configurations
    test_configs = [
        {
            "name": "EMA200 OFF (baseline)",
            "ema200_filter": False,
            "max_trades": 0,
            "expected": "Normal trade count"
        },
        {
            "name": "EMA200 ON, max_trades=0 (should block ~85% of trades)",
            "ema200_filter": True,
            "max_trades": 0,
            "expected": "Dramatically fewer trades"
        },
        {
            "name": "EMA200 ON, max_trades=1 (should allow limited trades)",
            "ema200_filter": True,
            "max_trades": 1,
            "expected": "More trades than max_trades=0, but still limited"
        }
    ]
    
    results = []
    
    for test_config in test_configs:
        print(f"\n📊 {test_config['name']}")
        print(f"   Expected: {test_config['expected']}")
        print("-" * 40)
        
        # Create configuration
        config = FullAnalysisConfig()
        config.CURRENT_DATASET_SIZE = 50000  # Use smaller dataset for quick test
        config.GLOBAL_EMA200_FILTER = test_config['ema200_filter']
        config.GLOBAL_EMA200_BELOW_MAX_TRADES = test_config['max_trades']
        config.USE_OPTIMIZED_RULES_ONLY = True
        config.MAX_CONCURRENT_TRADES = 1  # Single position for simplicity
        
        try:
            # Initialize engine
            engine = BacktestingEngine(config)
            print(f"   ✅ Engine initialized with {len(engine.df):,} candles")
            
            # Run a quick test with one rule
            if hasattr(engine, 'all_buy_rules') and engine.all_buy_rules:
                rule_name, rule_func = engine.all_buy_rules[0]  # Use first rule
                print(f"   🔍 Testing with rule: {rule_name}")
                
                # Run isolated backtest
                result = engine._run_isolated_rule_backtest(rule_name, rule_func, 0, len(engine.df))
                
                trade_count = len(result.get('trades', []))
                signal_count = result.get('signals', 0)
                
                results.append({
                    'config': test_config['name'],
                    'ema200_filter': test_config['ema200_filter'],
                    'max_trades': test_config['max_trades'],
                    'trade_count': trade_count,
                    'signal_count': signal_count
                })
                
                print(f"   📈 Results:")
                print(f"     - Signals generated: {signal_count}")
                print(f"     - Trades executed: {trade_count}")
                
            else:
                print(f"   ❌ No buy rules available for testing")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            continue
    
    # Compare results
    print(f"\n📈 COMPARISON RESULTS:")
    print("=" * 50)
    
    baseline_trades = None
    for result in results:
        print(f"\n🔹 {result['config']}")
        print(f"   - EMA200 Filter: {result['ema200_filter']}")
        print(f"   - Max Trades: {result['max_trades']}")
        print(f"   - Signals: {result['signal_count']}")
        print(f"   - Trades: {result['trade_count']}")
        
        if not result['ema200_filter']:
            baseline_trades = result['trade_count']
        elif baseline_trades is not None:
            reduction = ((baseline_trades - result['trade_count']) / baseline_trades) * 100
            print(f"   - Reduction vs baseline: {reduction:.1f}%")
    
    # Validate results
    print(f"\n🎯 VALIDATION:")
    if len(results) >= 2:
        ema_off = next((r for r in results if not r['ema200_filter']), None)
        ema_on_0 = next((r for r in results if r['ema200_filter'] and r['max_trades'] == 0), None)
        ema_on_1 = next((r for r in results if r['ema200_filter'] and r['max_trades'] == 1), None)
        
        if ema_off and ema_on_0:
            if ema_on_0['trade_count'] < ema_off['trade_count']:
                reduction = ((ema_off['trade_count'] - ema_on_0['trade_count']) / ema_off['trade_count']) * 100
                print(f"   ✅ EMA200 filter working: {reduction:.1f}% reduction in trades")
            else:
                print(f"   ❌ EMA200 filter NOT working: No reduction in trades")
        
        if ema_on_0 and ema_on_1:
            if ema_on_1['trade_count'] > ema_on_0['trade_count']:
                print(f"   ✅ max_trades logic working: max_trades=1 allows more trades than max_trades=0")
            else:
                print(f"   ❌ max_trades logic NOT working: No difference between max_trades=0 and max_trades=1")

if __name__ == "__main__":
    try:
        test_ema200_real_trading()
        
        print(f"\n🎉 REAL TRADING TEST COMPLETED!")
        print("If you see reduction percentages above, the EMA200 filter is working!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
