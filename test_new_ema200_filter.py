#!/usr/bin/env python3
"""
Test script for the NEW EMA200 filter implementation
Tests proper daily candle conversion and trade limiting functionality
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import FullAnalysisConfig
from backtesting_engine import BacktestingEngine, check_ema200_filter_worker

def test_new_ema200_filter():
    """Test the new EMA200 filter implementation"""
    print("🔧 TESTING NEW EMA200 FILTER IMPLEMENTATION")
    print("=" * 60)
    
    # Test with full dataset to ensure we have enough data for EMA200
    config = FullAnalysisConfig()
    
    print(f"📊 Configuration:")
    print(f"   - GLOBAL_EMA200_FILTER: {config.GLOBAL_EMA200_FILTER}")
    print(f"   - GLOBAL_EMA200_BELOW_MAX_TRADES: {config.GLOBAL_EMA200_BELOW_MAX_TRADES}")
    print(f"   - MIN_EMA200_DATASET_SIZE: {config.MIN_EMA200_DATASET_SIZE:,} candles")
    print(f"   - CURRENT_DATASET_SIZE: {config.CURRENT_DATASET_SIZE}")
    
    try:
        # Initialize engine with full dataset
        print(f"\n📈 Initializing engine with full dataset...")
        engine = BacktestingEngine(config)
        
        total_candles = len(engine.df)
        print(f"   ✅ Engine initialized with {total_candles:,} candles")
        
        # Check if we have sufficient data for EMA200
        if total_candles < config.MIN_EMA200_DATASET_SIZE:
            print(f"   ⚠️  WARNING: Only {total_candles:,} candles available")
            print(f"   ⚠️  Need minimum {config.MIN_EMA200_DATASET_SIZE:,} candles for reliable EMA200")
            print(f"   ⚠️  EMA200 filter may not work properly with insufficient data")
        else:
            print(f"   ✅ Sufficient data: {total_candles:,} >= {config.MIN_EMA200_DATASET_SIZE:,} candles")
        
        # Check EMA200 data availability
        test_ema200_data_quality(engine)
        
        # Test filter logic
        test_filter_logic_scenarios(engine, config)
        
        # Test trade limiting functionality
        test_trade_limiting(engine, config)
        
    except Exception as e:
        print(f"   ❌ Error initializing engine: {e}")
        import traceback
        traceback.print_exc()

def test_ema200_data_quality(engine):
    """Test the quality and availability of EMA200 data"""
    print(f"\n📊 TESTING EMA200 DATA QUALITY")
    print("-" * 40)
    
    if 'DAILY_EMA_200' not in engine.df.columns:
        print(f"   ❌ DAILY_EMA_200 column not found in dataframe")
        return False
    
    # Check EMA200 data statistics
    ema200_series = engine.df['DAILY_EMA_200']
    total_points = len(ema200_series)
    valid_points = ema200_series.notna().sum()
    invalid_points = ema200_series.isna().sum()
    
    print(f"   📈 EMA200 Data Statistics:")
    print(f"     - Total data points: {total_points:,}")
    print(f"     - Valid EMA200 values: {valid_points:,}")
    print(f"     - Invalid/NaN values: {invalid_points:,}")
    print(f"     - Coverage: {(valid_points/total_points)*100:.1f}%")
    
    if valid_points > 0:
        first_valid_idx = ema200_series.first_valid_index()
        last_valid_idx = ema200_series.last_valid_index()
        
        print(f"     - First valid EMA200 at index: {first_valid_idx:,} (~day {first_valid_idx/1440:.0f})")
        print(f"     - Last valid EMA200 at index: {last_valid_idx:,}")
        
        # Check EMA200 value range
        min_ema = ema200_series.min()
        max_ema = ema200_series.max()
        print(f"     - EMA200 range: ${min_ema:.0f} to ${max_ema:.0f}")
        
        # Test price vs EMA200 distribution
        valid_data = engine.df[ema200_series.notna()]
        below_ema_count = (valid_data['close'] < valid_data['DAILY_EMA_200']).sum()
        above_ema_count = (valid_data['close'] >= valid_data['DAILY_EMA_200']).sum()
        
        print(f"     - Price below EMA200: {below_ema_count:,} points ({(below_ema_count/len(valid_data))*100:.1f}%)")
        print(f"     - Price above EMA200: {above_ema_count:,} points ({(above_ema_count/len(valid_data))*100:.1f}%)")
        
        if below_ema_count > 0:
            print(f"   ✅ EMA200 data looks good - sufficient points below EMA for testing")
            return True
        else:
            print(f"   ⚠️  No points below EMA200 found - may limit filter testing")
            return False
    else:
        print(f"   ❌ No valid EMA200 data found")
        return False

def test_filter_logic_scenarios(engine, config):
    """Test different filter logic scenarios"""
    print(f"\n🧪 TESTING FILTER LOGIC SCENARIOS")
    print("-" * 40)
    
    if 'DAILY_EMA_200' not in engine.df.columns:
        print(f"   ❌ Cannot test - DAILY_EMA_200 not available")
        return
    
    # Find test indices with valid EMA200 data
    valid_ema_mask = engine.df['DAILY_EMA_200'].notna()
    valid_indices = engine.df[valid_ema_mask].index.tolist()
    
    if len(valid_indices) < 100:
        print(f"   ⚠️  Insufficient valid EMA200 data for testing ({len(valid_indices)} points)")
        return
    
    # Test scenarios
    scenarios = [
        {
            "name": "Price ABOVE EMA200 (should allow)",
            "filter_func": lambda row: row['close'] >= row['DAILY_EMA_200'],
            "expected_result": True
        },
        {
            "name": "Price BELOW EMA200 with max_trades=0 (should block)",
            "filter_func": lambda row: row['close'] < row['DAILY_EMA_200'],
            "expected_result": False,
            "max_trades": 0
        },
        {
            "name": "Price BELOW EMA200 with max_trades=1 (should allow first)",
            "filter_func": lambda row: row['close'] < row['DAILY_EMA_200'],
            "expected_result": True,
            "max_trades": 1
        }
    ]
    
    for scenario in scenarios:
        print(f"\n   📋 Scenario: {scenario['name']}")
        
        # Find indices matching the scenario condition
        test_data = engine.df[valid_ema_mask]
        matching_indices = []
        
        for idx in valid_indices[:1000]:  # Test first 1000 valid points
            row = engine.df.iloc[idx]
            if scenario['filter_func'](row):
                matching_indices.append(idx)
                if len(matching_indices) >= 10:  # Test 10 points per scenario
                    break
        
        if len(matching_indices) == 0:
            print(f"     ⚠️  No matching data points found for this scenario")
            continue
        
        print(f"     🔍 Testing {len(matching_indices)} data points...")
        
        # Test the filter
        allowed_count = 0
        blocked_count = 0
        
        for idx in matching_indices:
            # Test main engine filter
            test_config = config
            if 'max_trades' in scenario:
                test_config.GLOBAL_EMA200_BELOW_MAX_TRADES = scenario['max_trades']
            
            filter_result = engine._check_ema200_filter_main_engine(idx)
            
            if filter_result:
                allowed_count += 1
            else:
                blocked_count += 1
        
        print(f"     📊 Results: {allowed_count} allowed, {blocked_count} blocked")
        
        # Validate results
        if scenario['expected_result']:
            if allowed_count > blocked_count:
                print(f"     ✅ CORRECT: Most trades allowed as expected")
            else:
                print(f"     ❌ ERROR: Expected more trades to be allowed")
        else:
            if blocked_count > allowed_count:
                print(f"     ✅ CORRECT: Most trades blocked as expected")
            else:
                print(f"     ❌ ERROR: Expected more trades to be blocked")

def test_trade_limiting(engine, config):
    """Test trade limiting functionality with mock positions"""
    print(f"\n🎯 TESTING TRADE LIMITING FUNCTIONALITY")
    print("-" * 40)
    
    if 'DAILY_EMA_200' not in engine.df.columns:
        print(f"   ❌ Cannot test - DAILY_EMA_200 not available")
        return
    
    # Find a point where price is below EMA200
    valid_data = engine.df[engine.df['DAILY_EMA_200'].notna()]
    below_ema_data = valid_data[valid_data['close'] < valid_data['DAILY_EMA_200']]
    
    if len(below_ema_data) == 0:
        print(f"   ⚠️  No data points with price below EMA200 found")
        return
    
    test_idx = below_ema_data.index[0]
    print(f"   🔍 Testing at index {test_idx} (price below EMA200)")
    
    # Test with different position scenarios
    test_scenarios = [
        {"positions": [], "max_trades": 1, "expected": True, "desc": "No positions, max_trades=1"},
        {"positions": [create_mock_position_below_ema(test_idx, engine)], "max_trades": 1, "expected": False, "desc": "1 position below EMA, max_trades=1"},
        {"positions": [create_mock_position_above_ema(test_idx, engine)], "max_trades": 1, "expected": True, "desc": "1 position above EMA, max_trades=1"},
        {"positions": [], "max_trades": 0, "expected": False, "desc": "No positions, max_trades=0"},
    ]
    
    for scenario in test_scenarios:
        print(f"\n     📋 {scenario['desc']}")
        
        # Set up test configuration
        test_config = {
            'ENABLE_GLOBAL_MARKET_FILTERS': True,
            'GLOBAL_EMA200_FILTER': True,
            'GLOBAL_EMA200_BELOW_MAX_TRADES': scenario['max_trades'],
        }
        
        # Test worker filter
        essential_columns = ['open', 'high', 'low', 'close', 'volume', 'DAILY_EMA_200']
        worker_df = engine.df[essential_columns]
        
        result = check_ema200_filter_worker(worker_df, test_idx, test_config, [], scenario['positions'])
        
        if result == scenario['expected']:
            print(f"       ✅ CORRECT: Filter returned {result} (expected {scenario['expected']})")
        else:
            print(f"       ❌ ERROR: Filter returned {result} (expected {scenario['expected']})")

def create_mock_position_below_ema(idx, engine):
    """Create a mock position that was opened below EMA200"""
    ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
    entry_price = ema200_value * 0.95  # 5% below EMA200
    
    return {
        'entry_time': f"Index_{idx}",
        'entry_price': entry_price,
        'quantity': 100,
        'position_value': 1000,
        'entry_idx': idx
    }

def create_mock_position_above_ema(idx, engine):
    """Create a mock position that was opened above EMA200"""
    ema200_value = engine.df['DAILY_EMA_200'].iloc[idx]
    entry_price = ema200_value * 1.05  # 5% above EMA200
    
    return {
        'entry_time': f"Index_{idx}",
        'entry_price': entry_price,
        'quantity': 100,
        'position_value': 1000,
        'entry_idx': idx
    }

if __name__ == "__main__":
    try:
        test_new_ema200_filter()
        
        print(f"\n🎉 NEW EMA200 FILTER TEST COMPLETED!")
        print("=" * 60)
        print("The new EMA200 filter has been implemented with:")
        print("  ✅ Proper daily candle conversion (not minute-based)")
        print("  ✅ Trade limiting when price below EMA200")
        print("  ✅ Position tracking for concurrent trade limits")
        print("  ✅ Minimum dataset size requirements (288k candles)")
        print("\nYou can now test your trading strategy with confidence that:")
        print("  - EMA200 is calculated correctly from daily candles")
        print("  - max_trades=0 will block ALL trades when price < EMA200")
        print("  - max_trades=1 will allow MAX 1 concurrent trade when price < EMA200")
        print("  - The filter will have a significant impact on trade count")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
